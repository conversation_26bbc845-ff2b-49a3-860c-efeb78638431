import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Play, Code, Zap, Heart, Star, Mic, <PERSON>, Gift, Crown } from 'lucide-react';
import { Navbar } from '../components/layout/Navbar';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { ParticleWave } from '../components/animations/ParticleWave';
import { WaveBackground } from '../components/animations/WaveBackground';
import { useAuth } from '../contexts/AuthContext';

export const LandingPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [activeCodeTab, setActiveCodeTab] = useState('python');

  const codeExamples = {
    python: `import requests

response = requests.post('https://tts.scsmtech.cn/tts',
  headers={'Authorization': 'Bearer YOUR_API_KEY'},
  json={
    'text': '你好，欢迎使用 SoulVoice！',
    'model': 'FunAudioLLM/CosyVoice2-0.5B',
    'voice': 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    'speed': 1.0
  }
)

audio_data = response.content
with open('output.mp3', 'wb') as f:
    f.write(audio_data)`,

    javascript: `const response = await fetch('https://tts.scsmtech.cn/tts', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '你好，欢迎使用 SoulVoice！',
    model: 'FunAudioLLM/CosyVoice2-0.5B',
    voice: 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    speed: 1.0
  })
});

const audioBlob = await response.blob();
const audioUrl = URL.createObjectURL(audioBlob);`,

    curl: `curl -X POST https://tts.scsmtech.cn/tts \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "你好，欢迎使用 SoulVoice！",
    "model": "FunAudioLLM/CosyVoice2-0.5B",
    "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr",
    "speed": 1.0
  }' \\
  --output output.mp3`
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white overflow-hidden">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center">
        <ParticleWave />
        <WaveBackground />
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h1 className="text-6xl md:text-8xl font-bold">
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-blue-500 bg-clip-text text-transparent">
                赋予每个应用
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                说话的灵魂
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-2xl mx-auto">
              SoulVoice 提供业界最先进的语音合成与克隆技术，让您的应用拥有真正的声音表现力
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to={isAuthenticated ? "/dashboard" : "/login"}>
                <Button variant="primary" size="lg" glow className="px-8 py-4">
                  <Play className="h-5 w-5 mr-2" />
                  免费生成 10,000 字符
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6">为什么选择 SoulVoice？</h2>
            <p className="text-xl text-gray-400">三大核心优势，让您的应用脱颖而出</p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: '极致易用',
                description: '一行代码即可集成，3分钟完成部署。支持 REST API、SDK 和 Webhook，开发者友好。',
                color: 'from-yellow-400 to-orange-500'
              },
              {
                icon: Heart,
                title: '超高表现力',
                description: '业界领先的情感表达技术，支持 20+ 种情感风格，让每个字都充满生命力。',
                color: 'from-pink-400 to-red-500'
              },
              {
                icon: Star,
                title: '颠覆性性价比',
                description: '相比传统方案节省 70% 成本，提供免费额度和灵活的按需计费模式。',
                color: 'from-purple-400 to-blue-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="group"
              >
                <Card glass glow className="h-full text-center hover:bg-white/10 transition-all duration-300">
                  <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${feature.color} mb-6`}>
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 bg-gray-800/50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6">选择适合您的定价方案</h2>
            <p className="text-xl text-gray-400">灵活的定价模式，满足不同规模的需求</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* 免费版 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card glass className="h-full">
                <div className="p-6 text-center h-full flex flex-col">
                  <div className="mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                      <Gift className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">免费版</h3>
                    <div className="text-3xl font-bold text-white mb-1">¥0</div>
                    <p className="text-gray-400 text-sm">永久免费</p>
                  </div>

                  <div className="flex-1 space-y-3 mb-6">
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">10,000 字符/月</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">基础音色</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">标准音质</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">API 访问</span>
                    </div>
                  </div>

                  <Link to="/login">
                    <Button variant="glass" className="w-full">
                      免费开始
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>

            {/* 基础版 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card glass className="h-full">
                <div className="p-6 text-center h-full flex flex-col">
                  <div className="mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                      <Zap className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">基础版</h3>
                    <div className="text-3xl font-bold text-white mb-1">¥6.90</div>
                    <p className="text-gray-400 text-sm">每月</p>
                  </div>

                  <div className="flex-1 space-y-3 mb-6">
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">20万字节/月</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">基础语音合成</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">标准音质</span>
                    </div>
                  </div>

                  <Link to="/subscription">
                    <Button variant="primary" glow className="w-full">
                      立即订阅
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>

            {/* 标准版 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card glass className="h-full ring-2 ring-purple-500">
                <div className="p-6 text-center h-full flex flex-col relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      推荐
                    </span>
                  </div>

                  <div className="mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Star className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">标准版</h3>
                    <div className="text-3xl font-bold text-white mb-1">¥39.90</div>
                    <p className="text-gray-400 text-sm">每月</p>
                  </div>

                  <div className="flex-1 space-y-3 mb-6">
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">100万字节/月</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">高级语音合成</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">高清音质</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">优先支持</span>
                    </div>
                  </div>

                  <Link to="/subscription">
                    <Button variant="primary" glow className="w-full">
                      立即订阅
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>

            {/* 专业版 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card glass className="h-full">
                <div className="p-6 text-center h-full flex flex-col">
                  <div className="mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                      <Crown className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">专业版</h3>
                    <div className="text-3xl font-bold text-white mb-1">¥68.80</div>
                    <p className="text-gray-400 text-sm">每月</p>
                  </div>

                  <div className="flex-1 space-y-3 mb-6">
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">200万字节/月</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">专业语音合成</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">超清音质</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">专属客服</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                      <span className="text-gray-300">API优先级</span>
                    </div>
                  </div>

                  <Link to="/subscription">
                    <Button variant="primary" glow className="w-full">
                      立即订阅
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>
          </div>

          {/* 企业定制 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-16 text-center"
          >
            <Card glass className="p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold mb-4">需要更多？</h3>
              <p className="text-gray-400 mb-6">
                我们为企业客户提供定制化解决方案，包括私有部署、专属音色训练、技术集成支持等服务
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="glass" size="lg">
                  联系销售
                </Button>
                <Button variant="ghost" size="lg">
                  查看企业方案
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Code Example */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-6">开发者友好的 API</h2>
            <p className="text-xl text-gray-400">简单到只需一行代码</p>
          </motion.div>

          <Card glass className="p-8">
            <div className="space-y-4">
              <div className="flex space-x-2 border-b border-gray-700">
                {['python', 'javascript', 'curl'].map((lang) => (
                  <button
                    key={lang}
                    onClick={() => setActiveCodeTab(lang)}
                    className={`px-4 py-2 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                      activeCodeTab === lang
                        ? 'text-purple-400 bg-gray-800 border-b-2 border-purple-400'
                        : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800/50'
                    }`}
                  >
                    {lang.charAt(0).toUpperCase() + lang.slice(1)}
                  </button>
                ))}
              </div>

              <div className="bg-gray-900 rounded-lg p-6 font-mono text-sm border border-gray-700">
                <pre className="text-green-400 overflow-x-auto">
                  <code>{codeExamples[activeCodeTab as keyof typeof codeExamples]}</code>
                </pre>
              </div>

              <div className="text-center">
                <Button
                  variant="glass"
                  className="px-6"
                  onClick={() => navigator.clipboard.writeText(codeExamples[activeCodeTab as keyof typeof codeExamples])}
                >
                  <Code className="h-4 w-4 mr-2" />
                  复制代码
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-purple-900/20 to-blue-900/20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <h2 className="text-4xl font-bold">
              准备好让您的应用开口说话了吗？
            </h2>
            <p className="text-xl text-gray-400">
              加入数千名开发者的行列，开始使用 SoulVoice
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to={isAuthenticated ? "/dashboard" : "/login"}>
                <Button variant="primary" size="lg" glow className="px-8 py-4">
                  立即免费开始
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Mic className="h-6 w-6 text-purple-500" />
                <span className="text-lg font-bold">SoulVoice</span>
              </div>
              <p className="text-gray-400 text-sm">
                赋予每个应用说话的灵魂
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">产品</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">语音合成</a></li>
                <li><a href="#" className="hover:text-white">语音克隆</a></li>
                <li><a href="#" className="hover:text-white">API 文档</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">支持</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">帮助中心</a></li>
                <li><a href="#" className="hover:text-white">联系我们</a></li>
                <li><a href="#" className="hover:text-white">状态页面</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">公司</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">关于我们</a></li>
                <li><a href="#" className="hover:text-white">隐私政策</a></li>
                <li><a href="#" className="hover:text-white">服务条款</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; 2024 SoulVoice. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};