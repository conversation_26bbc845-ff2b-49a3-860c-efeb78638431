import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Volume2, Home, Settings, LogOut } from 'lucide-react';
import { Button } from '../ui/Button';
import { useAuth } from '../../contexts/AuthContext';

export const Navbar: React.FC = () => {
  const location = useLocation();
  const { isAuthenticated, logout } = useAuth();

  const isLandingPage = location.pathname === '/';

  if (isLandingPage) {
    return (
      <nav className="fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <img src="/logo-32x32.png" alt="SoulVoice Logo" className="h-8 w-8" />
              <span className="text-xl font-bold text-white">SoulVoice</span>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                功能
              </a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                定价
              </a>
              <Link to="/docs" className="text-gray-300 hover:text-white transition-colors">
                文档
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              {isAuthenticated ? (
                <Link to="/dashboard">
                  <Button variant="primary" glow>
                    进入控制台
                  </Button>
                </Link>
              ) : (
                <>
                  <Link to="/login">
                    <Button variant="glass">
                      登录
                    </Button>
                  </Link>
                  <Link to="/register">
                    <Button variant="primary" glow>
                      注册
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>
    );
  }

  return null;
};