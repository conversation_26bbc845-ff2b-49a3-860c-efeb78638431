# TTS API 404 错误修复

## 🐛 问题描述

用户在点击生成音频时遇到 "API 请求失败: 404 - 接口不存在" 的错误。

## 🔍 问题分析

### 1. 初始问题
- 代码中使用了未定义的 `this.API_URL` 和 `this.API_KEY`
- 应该使用 `this.INTERNAL_API_URL` 和 `this.INTERNAL_API_KEY`

### 2. 根本问题
经过深入分析发现，真正的问题是 **CORS (跨域资源共享) 限制**：

- 前端应用运行在 `http://localhost:5174`
- 尝试直接调用 `https://api.siliconflow.cn/v1/audio/speech`
- 浏览器的同源策略阻止了这种跨域请求

### 3. 验证测试
通过 curl 命令验证 API 端点是正确的：

```bash
# 通过代理测试 API 调用
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

curl -X POST https://api.siliconflow.cn/v1/audio/speech \
  -H "Authorization: Bearer sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "FunAudioLLM/CosyVoice2-0.5B",
    "input": "你好",
    "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr"
  }'
```

**结果**: API 调用成功，返回音频数据，证明端点和密钥都是正确的。

## 🔧 解决方案

### 方案选择
项目中已经有 Supabase Functions 作为代理服务，这是解决 CORS 问题的标准做法：

1. **前端** → **Supabase Functions** → **SiliconFlow API**
2. 避免浏览器 CORS 限制
3. 保护 API 密钥不暴露在前端代码中

### 代码修改

#### 修复前 (直接调用 SiliconFlow API)
```typescript
// 发送请求到 SiliconFlow API
const response = await fetch(this.INTERNAL_API_URL, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${this.INTERNAL_API_KEY}`,
    'Content-Type': 'application/json',
    'User-Agent': 'SoulVoice/1.0.0',
  },
  body: JSON.stringify(requestBody),
});
```

#### 修复后 (通过 Supabase Functions)
```typescript
// 发送请求到 Supabase Functions (避免 CORS 问题)
const response = await fetch(`${this.PUBLIC_API_URL}/tts`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'SoulVoice/1.0.0',
  },
  body: JSON.stringify({
    text: request.input,
    voice: request.voice,
    model: requestBody.model,
    speed: requestBody.speed,
    gain: requestBody.gain,
    response_format: requestBody.response_format,
    sample_rate: requestBody.sample_rate,
    pitch: requestBody.pitch,
    emotion: requestBody.emotion,
  }),
});
```

## 🏗️ 架构说明

### 请求流程
```
前端应用 (localhost:5174)
    ↓
Supabase Functions (/tts)
    ↓
SiliconFlow API (api.siliconflow.cn)
```

### 优势
1. **解决 CORS 问题**: 通过服务端代理避免浏览器限制
2. **API 密钥安全**: 密钥存储在服务端环境变量中
3. **统一错误处理**: 在 Supabase Functions 中统一处理错误
4. **请求日志**: 可以在服务端记录请求日志

## 📁 相关文件

### 修改的文件
- `src/services/ttsService.ts` - 修改 API 调用方式

### 相关的 Supabase Functions
- `supabase/functions/tts/index.ts` - TTS 代理服务
- `supabase/functions/_shared/siliconflow.ts` - SiliconFlow 客户端封装

## 🧪 测试验证

### 1. API 端点测试
```bash
# 测试 Supabase Functions 端点
curl -X POST https://tts.scsmtech.cn/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，这是测试",
    "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr",
    "model": "FunAudioLLM/CosyVoice2-0.5B"
  }'
```

### 2. 前端功能测试
1. 打开音色实验室页面
2. 选择音色
3. 输入文本
4. 点击生成音频
5. 验证音频生成成功

## 🔄 向后兼容

### 保留的功能
- `generateSpeechWithApiKey` 方法仍然可用于外部 API 调用
- 所有原有的参数和选项都得到保留
- 错误处理机制保持不变

### 配置说明
- `INTERNAL_API_URL` 和 `INTERNAL_API_KEY` 仍然保留，用于服务端调用
- `PUBLIC_API_URL` 指向 Supabase Functions 端点
- 环境变量 `VITE_API_BASE_URL` 可以覆盖默认配置

## 🚀 部署注意事项

### 环境变量配置
确保 Supabase Functions 中配置了正确的环境变量：

```bash
# 在 Supabase 项目设置中配置
SILICONFLOW_API_KEY=sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw
```

### 函数部署
```bash
# 部署 Supabase Functions
supabase functions deploy tts
```

## 📝 总结

### 问题根因
- 浏览器 CORS 策略阻止直接跨域 API 调用
- 前端应用无法直接调用 SiliconFlow API

### 解决方案
- 使用 Supabase Functions 作为代理
- 保持 API 密钥在服务端安全
- 提供统一的错误处理和日志记录

### 效果
- ✅ 解决了 404 错误
- ✅ 提高了安全性
- ✅ 保持了功能完整性
- ✅ 维护了向后兼容性

---

**修复日期**: 2025-01-31  
**状态**: ✅ 已完成  
**测试**: ✅ 通过
