# 开发者工具更新说明

## 📋 更新概述

本次更新为 SoulVoice 开发者工具添加了模型参数支持，用户现在可以在开发者工具中选择不同的语音合成模型，并查看相应的示例代码。

## 🆕 新增功能

### 1. 模型选择器
- **位置**: 音色选择器下方
- **功能**: 允许用户选择语音合成模型
- **支持的模型**:
  - `FunAudioLLM/CosyVoice2-0.5B` (默认，推荐)
  - `fnlp/MOSS-TTSD-v0.5`

### 2. 更新的示例代码
所有示例代码（Python、JavaScript、cURL）现在包含：
- ✅ 正确的 API 端点地址
- ✅ 模型参数支持
- ✅ 实际可用的音色 ID
- ✅ 动态参数生成（根据用户选择）

### 3. 新增 API 文档
- **模型列表 API**: `GET /v1/models`
- **参数说明**: 详细的模型参数文档
- **响应示例**: 完整的 API 响应格式

## 🔧 技术实现

### 前端更新
1. **VoiceLab.tsx**:
   - 添加 `selectedModel` 状态
   - 新增模型选择 UI 组件
   - 更新代码示例生成逻辑
   - 集成 Cpu 图标

2. **Docs.tsx**:
   - 更新 API 参考文档
   - 添加模型参数说明
   - 新增模型列表 API 文档

### 示例代码更新

#### Python 示例
```python
import requests

response = requests.post('https://tts.scsmtech.cn/tts', 
  headers={'Authorization': 'Bearer YOUR_API_KEY'},
  json={
    'text': '用户输入的文本',
    'model': 'FunAudioLLM/CosyVoice2-0.5B',  # 新增
    'voice': 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    'speed': 1.0
  }
)
```

#### JavaScript 示例
```javascript
const response = await fetch('https://tts.scsmtech.cn/tts', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '用户输入的文本',
    model: 'FunAudioLLM/CosyVoice2-0.5B',  // 新增
    voice: 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    speed: 1.0
  })
});
```

#### cURL 示例
```bash
curl -X POST https://tts.scsmtech.cn/tts \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "用户输入的文本",
    "model": "FunAudioLLM/CosyVoice2-0.5B",
    "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr",
    "speed": 1.0
  }' \
  --output output.mp3
```

## 📊 测试验证

### 自动化测试
创建了 `scripts/test-developer-examples.sh` 脚本，验证：
- ✅ 所有示例代码格式正确
- ✅ API 端点地址有效
- ✅ 模型参数支持正常
- ✅ 生成的音频文件有效
- ✅ 模型列表 API 正常

### 测试结果
```
📊 测试结果总结
总测试数: 5
通过测试: 5
失败测试: 0

📁 生成的文件：
  example_basic.mp3 (34K) - MPEG ADTS, layer III, v2, 128 kbps, 24 kHz, Monaural
  example_cosyvoice2.mp3 (46K) - MPEG ADTS, layer III, v2, 128 kbps, 24 kHz, Monaural
  example_full.mp3 (35K) - MPEG ADTS, layer III, v2, 128 kbps, 24 kHz, Monaural
  example_moss.mp3 (76K) - MPEG ADTS, layer III, v2, 128 kbps, 24 kHz, Monaural
  example_models.json (705B) - JSON 响应
```

## 🎯 用户体验改进

### 1. 智能参数显示
- 只有在非默认值时才显示可选参数
- 动态生成代码，避免冗余

### 2. 模型推荐
- CosyVoice2-0.5B 标记为"推荐"
- 提供模型描述和特性说明

### 3. 实时更新
- 用户选择模型后，示例代码立即更新
- 保持与实际 API 的一致性

## 🔗 相关文件

### 更新的文件
- `src/pages/VoiceLab.tsx` - 主要的开发者工具页面
- `src/pages/Docs.tsx` - API 文档页面
- `scripts/test-developer-examples.sh` - 测试脚本

### 新增的文件
- `docs/DEVELOPER_TOOLS_UPDATE.md` - 本文档

## 🚀 后续计划

1. **音色兼容性显示**: 在模型选择时显示兼容的音色
2. **代码高亮优化**: 改进代码示例的语法高亮
3. **更多语言支持**: 添加更多编程语言的示例
4. **交互式测试**: 允许用户直接在开发者工具中测试 API

## 📝 总结

本次更新成功地将模型参数支持集成到开发者工具中，提供了：
- 🎯 **准确的示例代码**: 所有示例都经过实际测试验证
- 🔧 **完整的参数支持**: 包含所有新增的模型参数
- 📚 **详细的文档**: 更新了 API 参考文档
- ✅ **质量保证**: 通过自动化测试确保代码正确性

开发者现在可以轻松地在不同模型之间切换，并获得相应的示例代码，大大提升了 API 的易用性和开发体验。
